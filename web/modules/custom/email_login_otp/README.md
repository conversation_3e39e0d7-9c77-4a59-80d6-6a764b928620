Email Login OTP module for Drupal 10.x & 11.x.
This module adds Email based OTP authentication functionality to Drupal.

INSTALLATION INSTRUCTIONS
-------------------------

1.  Download the module and unzip it your Drupal /modules/ or /modules/contrib/ directory.
2.  Enable the module:
    a.  <PERSON><PERSON> as site administrator, visit the Extend page, and enable Email Login OTP.
    b.  Run "drush pm-enable email_login_otp" on the command line.
3.  Configure the module at `/admin/config/email_login_otp/config`
4.  Done!

FEATURES
--------
* Email-based OTP authentication for both web forms and REST API
* Configurable OTP enforcement (optional vs mandatory)
* User-specific OTP settings at `/user/{uid}/2fa-settings`
* Bypass permissions for privileged users
* Secure REST API login protection
* AJAX-based login form integration

SECURITY FEATURES
-----------------
* **REST API Protection**: Prevents bypass of OTP via `/user/login?_format=json`
* **Configurable Enforcement**: <PERSON><PERSON> can make OTP mandatory for all users
* **Bypass Permissions**: Fine-grained control via `email_login_otp bypass enforced redirect` permission
* **Token Validation**: Proper OTP token validation for API requests

CONFIGURATION
-------------
Visit `/admin/config/email_login_otp/config` to configure:
- Allow users to enable/disable OTP
- Force redirect for users without OTP
- Resend wait time
- Redirect messages

USAGE
-----

### Web Form Login
1. User enters username/password
2. If OTP is enabled, user receives email with OTP code
3. User enters OTP code to complete login

NOTES
-----
* Generated OTP is valid for 5 minutes (configurable)
* OTP codes are 6-digit random numbers
* Email templates are customizable via Twig templates
* Module respects Drupal's flood control for login attempts
