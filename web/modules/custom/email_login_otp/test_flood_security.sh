#!/bin/bash

# Test script to verify the flood control security fix
# This script tests the specific vulnerability where flood control could be bypassed

# Configuration
SITE_URL="http://localhost"  # Change this to your site URL
TEST_USERNAME="testuser"     # Change this to a test username (without OTP enabled)
WRONG_PASSWORD="wrongpass"   # Wrong password for testing
CORRECT_PASSWORD="testpass"  # Correct password for the test user

echo "=== Email Login OTP Flood Control Security Test ==="
echo "Site URL: $SITE_URL"
echo "Test User: $TEST_USERNAME"
echo ""

echo "This test verifies that the flood control bypass vulnerability is fixed."
echo "The vulnerability allowed login with any password after flood limit was reached."
echo ""

# Function to attempt login
attempt_login() {
    local username=$1
    local password=$2
    local attempt_num=$3
    
    echo "Attempt $attempt_num: Trying login with password '$password'"
    
    RESPONSE=$(curl -s -c cookies.txt -b cookies.txt \
        -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "name=$username&pass=$password&form_id=user_login_form&op=Log+in" \
        "$SITE_URL/user/login")
    
    if echo "$RESPONSE" | grep -q "Member for\|My account\|Log out"; then
        echo "  ❌ LOGIN SUCCESSFUL - This indicates a security vulnerability!"
        return 0
    elif echo "$RESPONSE" | grep -q "Too many failed login attempts"; then
        echo "  ✅ Blocked by flood control"
        return 1
    elif echo "$RESPONSE" | grep -q "Unrecognized username or password"; then
        echo "  ✅ Authentication failed (normal)"
        return 1
    else
        echo "  ⚠️  Unknown response"
        return 2
    fi
}

# Clean up any existing cookies
rm -f cookies.txt

echo "Step 1: Testing normal login with correct credentials..."
attempt_login "$TEST_USERNAME" "$CORRECT_PASSWORD" "1"
if [ $? -eq 0 ]; then
    echo "Normal login works. Logging out..."
    curl -s -b cookies.txt "$SITE_URL/user/logout" > /dev/null
    rm -f cookies.txt
fi

echo ""
echo "Step 2: Making multiple failed login attempts to trigger flood control..."

# Make multiple failed attempts (adjust number based on your flood settings)
for i in {1..5}; do
    attempt_login "$TEST_USERNAME" "$WRONG_PASSWORD" "$i"
    sleep 1
done

echo ""
echo "Step 3: Testing the vulnerability - attempting login with correct password after flood limit..."
echo "If the vulnerability exists, this will succeed despite flood control."

attempt_login "$TEST_USERNAME" "$CORRECT_PASSWORD" "FINAL"
RESULT=$?

echo ""
echo "=== Test Results ==="

if [ $RESULT -eq 0 ]; then
    echo "❌ VULNERABILITY DETECTED!"
    echo "The user was logged in despite flood control being active."
    echo "This indicates the security fix is NOT working properly."
    echo ""
    echo "IMMEDIATE ACTION REQUIRED:"
    echo "1. Apply the security patch"
    echo "2. Clear all caches: drush cache:rebuild"
    echo "3. Re-run this test to verify the fix"
elif [ $RESULT -eq 1 ]; then
    echo "✅ SECURITY FIX WORKING!"
    echo "Login was properly blocked by flood control."
    echo "The vulnerability has been successfully patched."
else
    echo "⚠️  INCONCLUSIVE RESULT"
    echo "Unable to determine if the fix is working."
    echo "Please check:"
    echo "1. Site URL is correct: $SITE_URL"
    echo "2. Test user exists: $TEST_USERNAME"
    echo "3. User does not have OTP enabled"
    echo "4. Flood control is configured with low limits"
fi

echo ""
echo "=== Additional Information ==="
echo "To properly test this vulnerability:"
echo "1. Create a test user account without OTP enabled"
echo "2. Configure flood control at /admin/config/people/flood-control"
echo "3. Set user limit to 3-5 attempts for testing"
echo "4. Run this script with correct credentials"
echo ""
echo "For more details, see: FLOOD-CONTROL-SECURITY-FIX.md"

# Clean up
rm -f cookies.txt
