diff --git a/README.md b/README.md
index 5b7aadc..2621879 100644
--- a/README.md
+++ b/README.md
@@ -1,4 +1,4 @@
-Email Login OTP module for Drupal 8.x & 9.x.
+Email Login OTP module for Drupal 10.x & 11.x.
 This module adds Email based OTP authentication functionality to Drupal.
 
 INSTALLATION INSTRUCTIONS
@@ -8,12 +8,44 @@ INSTALLATION INSTRUCTIONS
 2.  Enable the module:
     a.  <PERSON><PERSON> as site administrator, visit the Extend page, and enable Email Login OTP.
     b.  Run "drush pm-enable email_login_otp" on the command line.
-3.  No configurations needed.
+3.  Configure the module at `/admin/config/email_login_otp/config`
 4.  Done!
 
+FEATURES
+--------
+* Email-based OTP authentication for both web forms and REST API
+* Configurable OTP enforcement (optional vs mandatory)
+* User-specific OTP settings at `/user/{uid}/2fa-settings`
+* Bypass permissions for privileged users
+* Secure REST API login protection
+* AJAX-based login form integration
+
+SECURITY FEATURES
+-----------------
+* **REST API Protection**: Prevents bypass of OTP via `/user/login?_format=json`
+* **Configurable Enforcement**: Admin can make OTP mandatory for all users
+* **Bypass Permissions**: Fine-grained control via `email_login_otp bypass enforced redirect` permission
+* **Token Validation**: Proper OTP token validation for API requests
+
+CONFIGURATION
+-------------
+Visit `/admin/config/email_login_otp/config` to configure:
+- Allow users to enable/disable OTP
+- Force redirect for users without OTP
+- Resend wait time
+- Redirect messages
+
+USAGE
+-----
+
+### Web Form Login
+1. User enters username/password
+2. If OTP is enabled, user receives email with OTP code
+3. User enters OTP code to complete login
+
 NOTES
 -----
-* This module provides OTP authentication to Login form only.
-* This module overrides the default Login form submit callback and registers its' own ajax based callback.
-* Generated OTP is valid til 5 minutes.
-* No configrations needed.
+* Generated OTP is valid for 5 minutes (configurable)
+* OTP codes are 6-digit random numbers
+* Email templates are customizable via Twig templates
+* Module respects Drupal's flood control for login attempts
diff --git a/email_login_otp.info.yml b/email_login_otp.info.yml
index 49a66c1..983acb3 100644
--- a/email_login_otp.info.yml
+++ b/email_login_otp.info.yml
@@ -1,5 +1,5 @@
 name: 'Email Login OTP'
 type: module
 description: 'Provides login via Email OTP.'
-core_version_requirement: ^9.5 || ^10 || ^11
+core_version_requirement: ^10 || ^11
 configure: email_login_otp.email_login_otp_config_form
diff --git a/email_login_otp.install b/email_login_otp.install
index 6fac98c..466f9bd 100644
--- a/email_login_otp.install
+++ b/email_login_otp.install
@@ -95,3 +95,16 @@ function email_login_otp_update_10001() {
   ]);
   $schema->addPrimaryKey('otp_settings', ['uid']);
 }
+
+/**
+ * Security update: Clear caches to register new route subscriber service.
+ *
+ * This update ensures the new OtpLoginController is properly registered
+ * to protect the REST API login endpoint from OTP bypass.
+ */
+function email_login_otp_update_10002() {
+  // Clear all caches to ensure the new route subscriber is registered.
+  drupal_flush_all_caches();
+
+  return t('Security update applied: REST API login endpoint is now protected by OTP validation.');
+}
diff --git a/email_login_otp.services.yml b/email_login_otp.services.yml
index 6a08209..2ad27d7 100644
--- a/email_login_otp.services.yml
+++ b/email_login_otp.services.yml
@@ -4,6 +4,10 @@ services:
     arguments: ['@current_user', '@tempstore.private', '@email_login_otp.otp', '@current_route_match', '@config.factory', '@messenger', '@entity_type.manager']
     tags:
       - { name: event_subscriber }
+  email_login_otp.route_subscriber:
+    class: Drupal\email_login_otp\Routing\EmailLoginOtpRouteSubscriber
+    tags:
+      - { name: event_subscriber }
   email_login_otp.otp:
     class: Drupal\email_login_otp\Services\Otp
     arguments: ['@database', '@plugin.manager.mail', '@language_manager', '@password', '@tempstore.private', '@config.factory', '@extension.path.resolver', '@renderer']
diff --git a/src/EventSubscriber/OtpRedirectSubscriber.php b/src/EventSubscriber/OtpRedirectSubscriber.php
index ef57d2a..190b8ba 100644
--- a/src/EventSubscriber/OtpRedirectSubscriber.php
+++ b/src/EventSubscriber/OtpRedirectSubscriber.php
@@ -136,6 +136,7 @@ class OtpRedirectSubscriber implements EventSubscriberInterface {
       ];
       if (
         !$account->hasRole('administrator') &&
+        !$account->hasPermission('email_login_otp bypass enforced redirect') &&
         !$config->get('allow_enable_disable') &&
         !$this->email_login_otp->isEnabled($this->currentUser->id()) &&
         !in_array($this->routeMatch->getRouteName(), $bypass_routes) &&
