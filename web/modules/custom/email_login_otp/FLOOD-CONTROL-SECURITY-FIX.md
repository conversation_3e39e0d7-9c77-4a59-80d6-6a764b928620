# Email Login OTP Flood Control Security Fix

## Vulnerability Description

The email_login_otp module had a critical security vulnerability in its AJAX login callback that allowed authentication bypass when flood control limits were reached.

### The Problem

The original `email_login_otp_login_ajax_callback()` function had a fatal flaw in its logic:

1. It only checked for form validation errors: `if ($form_state->getErrors())`
2. When flood control is triggered, <PERSON><PERSON><PERSON> doesn't set form errors - it handles flood control differently
3. If no form errors were found, the code would:
   - Load the user by username: `$account = user_load_by_name($form_state->getValue('name'))`
   - Check if <PERSON><PERSON> is enabled: `if (!$otp->isEnabled($account->id()))`
   - **If <PERSON><PERSON> was not enabled, it would call `user_login_finalize($account)` WITHOUT validating the password!**

### Attack Scenario

1. Attacker targets a user account that doesn't have OTP enabled
2. Attacker makes multiple failed login attempts with wrong passwords
3. After reaching the flood limit (e.g., 5 attempts), flood control is triggered
4. Attacker makes one more login attempt with ANY password
5. Since flood control doesn't set form errors, the callback proceeds to check OTP status
6. Since OTP is not enabled, `user_login_finalize()` is called without password validation
7. **Attacker is logged in as the target user**

## Security Fix Implementation

### 1. Proper Credential Validation

Added explicit username/password validation before any authentication logic:

```php
// SECURITY FIX: Validate user credentials before proceeding.
$username = $form_state->getValue('name');
$password = $form_state->getValue('pass');

if (empty($username) || empty($password)) {
  // Handle error
}

// Load user and validate credentials.
$account = user_load_by_name($username);
if (!$account || !$account->isActive()) {
  // Handle error
}

// Authenticate the user credentials.
$user_auth = \Drupal::service('user.auth');
$authenticated_uid = $user_auth->authenticate($username, $password);

if (!$authenticated_uid || $authenticated_uid != $account->id()) {
  // Register failed attempt and handle error
}
```

### 2. Explicit Flood Control Checking

Added proper flood control validation before attempting authentication:

```php
// Check flood control before attempting authentication.
$flood = \Drupal::service('user.flood_control');
$config = \Drupal::config('user.flood');

// Check if this IP is blocked.
if (!$flood->isAllowed('user.failed_login_ip', $config->get('ip_limit'), $config->get('ip_window'))) {
  // Handle IP-based blocking
}

// Check if this user is blocked.
if (!$flood->isAllowed('user.failed_login_user', $config->get('user_limit'), $config->get('user_window'), $username)) {
  // Handle user-based blocking
}
```

### 3. Proper Flood Control Registration

Added flood control event registration for failed attempts and clearing for successful ones:

```php
// On authentication failure:
$flood->register('user.failed_login_ip', $config->get('ip_window'));
$flood->register('user.failed_login_user', $config->get('user_window'), $username);

// On authentication success:
$flood->clear('user.failed_login_ip');
$flood->clear('user.failed_login_user', $username);
```

## Security Improvements

### Before the Fix
- ❌ No password validation when flood control was active
- ❌ No explicit flood control checking
- ❌ Authentication bypass possible for users without OTP
- ❌ No proper flood control event registration

### After the Fix
- ✅ Explicit password validation always required
- ✅ Proper flood control checking before authentication
- ✅ No authentication bypass possible
- ✅ Proper flood control event registration and clearing
- ✅ Consistent security behavior with Drupal core login

## Testing the Fix

### Manual Testing
1. Configure flood control with low limits (e.g., 3 attempts)
2. Create a user without OTP enabled
3. Make multiple failed login attempts to trigger flood control
4. Attempt login with correct password - should be blocked
5. Verify user is NOT logged in

### Automated Testing
Run the included test:
```bash
./vendor/bin/phpunit web/modules/custom/email_login_otp/tests/src/Functional/EmailLoginOtpFloodSecurityTest.php
```

## Impact Assessment

### Severity: **CRITICAL**
- **CVSS Score**: 9.8 (Critical)
- **Attack Vector**: Network
- **Attack Complexity**: Low
- **Privileges Required**: None
- **User Interaction**: None
- **Scope**: Changed
- **Confidentiality Impact**: High
- **Integrity Impact**: High
- **Availability Impact**: High

### Affected Versions
- All versions of email_login_otp module prior to this security fix
- Affects users who have NOT enabled OTP (the majority of users in most installations)

### Mitigation
- Apply this security patch immediately
- Review access logs for suspicious login patterns
- Consider enabling OTP for all users if appropriate for your use case
- Monitor flood control logs for potential exploitation attempts

## Backward Compatibility

This fix maintains full backward compatibility:
- No API changes
- No configuration changes required
- No database schema changes
- Existing functionality preserved
- Only security behavior improved

## Credits

- **Vulnerability reported by**: [Reporter Name]
- **Security fix developed by**: [Developer Name]
- **Severity assessment**: Critical
- **Fix verification**: Comprehensive test suite included
