<?php

namespace Drupal\Tests\email_login_otp\Functional;

use <PERSON>upal\Tests\BrowserTestBase;
use <PERSON><PERSON><PERSON>\user\Entity\User;

/**
 * Tests the email login OTP flood control security fix.
 *
 * @group email_login_otp
 */
class EmailLoginOtpFloodSecurityTest extends BrowserTestBase {

  /**
   * {@inheritdoc}
   */
  protected $defaultTheme = 'stark';

  /**
   * {@inheritdoc}
   */
  protected static $modules = ['email_login_otp'];

  /**
   * A test user without OTP enabled.
   *
   * @var \Drupal\user\UserInterface
   */
  protected $testUser;

  /**
   * {@inheritdoc}
   */
  protected function setUp(): void {
    parent::setUp();

    // Create a test user without OTP enabled.
    $this->testUser = $this->drupalCreateUser();

    // Configure flood control with low limits for testing.
    $this->config('user.flood')
      ->set('ip_limit', 3)
      ->set('ip_window', 3600)
      ->set('user_limit', 3)
      ->set('user_window', 3600)
      ->save();

    // Configure email_login_otp to allow users without OTP.
    $this->config('email_login_otp.config')
      ->set('allow_enable_disable', TRUE)
      ->save();
  }

  /**
   * Tests that flood control prevents login bypass vulnerability.
   */
  public function testFloodControlPreventsLoginBypass() {
    $username = $this->testUser->getAccountName();
    $correct_password = $this->testUser->passRaw;
    $wrong_password = 'wrong_password_123';

    // Test 1: Verify normal login works with correct credentials.
    $this->drupalGet('/user/login');
    $this->submitForm([
      'name' => $username,
      'pass' => $correct_password,
    ], 'Log in');
    $this->assertSession()->pageTextContains('Member for');
    $this->drupalLogout();

    // Test 2: Attempt multiple failed logins to trigger flood control.
    for ($i = 0; $i < 4; $i++) {
      $this->drupalGet('/user/login');
      $this->submitForm([
        'name' => $username,
        'pass' => $wrong_password,
      ], 'Log in');
      
      if ($i < 3) {
        // Should show normal error message for first 3 attempts.
        $this->assertSession()->pageTextContains('Unrecognized username or password');
      } else {
        // 4th attempt should trigger flood control.
        $this->assertSession()->pageTextContains('Too many failed login attempts');
      }
    }

    // Test 3: Verify that even with correct password, login is blocked due to flood.
    $this->drupalGet('/user/login');
    $this->submitForm([
      'name' => $username,
      'pass' => $correct_password,
    ], 'Log in');
    
    // Should still be blocked by flood control.
    $this->assertSession()->pageTextContains('Too many failed login attempts');
    
    // Verify user is NOT logged in (this was the vulnerability).
    $this->assertSession()->pageTextNotContains('Member for');
    $this->assertSession()->addressEquals('/user/login');
  }

  /**
   * Tests that AJAX login also respects flood control.
   */
  public function testAjaxLoginRespectsFloodControl() {
    $username = $this->testUser->getAccountName();
    $wrong_password = 'wrong_password_123';

    // Enable AJAX on login form by visiting the page.
    $this->drupalGet('/user/login');

    // Simulate multiple failed AJAX login attempts.
    for ($i = 0; $i < 4; $i++) {
      $this->drupalPostAjaxForm('/user/login', [
        'name' => $username,
        'pass' => $wrong_password,
      ], 'Log in');
    }

    // After flood limit is reached, even correct password should be blocked.
    $response = $this->drupalPostAjaxForm('/user/login', [
      'name' => $username,
      'pass' => $this->testUser->passRaw,
    ], 'Log in');

    // Verify flood control message is shown.
    $this->assertStringContainsString('Too many failed login attempts', $response);
    
    // Verify user is not logged in.
    $this->assertFalse($this->loggedInUser);
  }

  /**
   * Tests that IP-based flood control works.
   */
  public function testIpBasedFloodControl() {
    // Create multiple users to test IP-based limiting.
    $user1 = $this->drupalCreateUser();
    $user2 = $this->drupalCreateUser();
    $user3 = $this->drupalCreateUser();

    $wrong_password = 'wrong_password_123';

    // Attempt failed logins with different users from same IP.
    $this->drupalGet('/user/login');
    $this->submitForm(['name' => $user1->getAccountName(), 'pass' => $wrong_password], 'Log in');
    
    $this->drupalGet('/user/login');
    $this->submitForm(['name' => $user2->getAccountName(), 'pass' => $wrong_password], 'Log in');
    
    $this->drupalGet('/user/login');
    $this->submitForm(['name' => $user3->getAccountName(), 'pass' => $wrong_password], 'Log in');

    // 4th attempt should trigger IP-based flood control.
    $this->drupalGet('/user/login');
    $this->submitForm(['name' => $user1->getAccountName(), 'pass' => $user1->passRaw], 'Log in');
    
    $this->assertSession()->pageTextContains('Too many failed login attempts from your IP address');
  }

  /**
   * Helper method to submit AJAX form.
   */
  protected function drupalPostAjaxForm($path, array $edit, $triggering_element) {
    $this->drupalGet($path);
    
    // Fill in the form.
    foreach ($edit as $field => $value) {
      $this->getSession()->getPage()->fillField($field, $value);
    }
    
    // Click the submit button.
    $this->getSession()->getPage()->pressButton($triggering_element);
    
    // Return the page content for assertion.
    return $this->getSession()->getPage()->getContent();
  }

}
