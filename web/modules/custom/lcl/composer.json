{"name": "drupal/lcl", "type": "drupal-module", "description": "LC Lightbox integration for Drupal - A responsive lightbox jQuery plugin for images, videos, and other content based on LC-Lightbox library.", "keywords": ["drupal", "lightbox", "image", "slider", "gallery", "responsive", "j<PERSON>y"], "homepage": "https://www.drupal.org/project/lcl", "license": "GPL-2.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "support": {"issues": "https://www.drupal.org/project/issues/lcl", "source": "https://git.drupalcode.org/project/lcl"}, "require": {"php": ">=8.1", "drupal/core": "^10 || ^11"}, "require-dev": {"drupal/core-dev": "^10 || ^11"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "allow-plugins": {"composer/installers": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}, "extra": {"drupal": {"version": "1.0.0-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Dev releases are not covered by Drupal security advisories."}}}}